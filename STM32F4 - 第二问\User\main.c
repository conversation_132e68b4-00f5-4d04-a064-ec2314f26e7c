/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：高精度DDS正弦信号生成
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动
#include "../Modules/Generation/dds_wavegen.h" // 高精度DDS波形生成

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* 初始化DAC8552和高精度DDS */
    DAC8552_Init();  // 初始化DAC8552

    // 配置高精度DDS参数
    DDS_Config_t dds_config = {
        .frequency = 1000,              // 初始频率1kHz
        .amplitude = 2048,              // 幅度 (12位DAC中心值)
        .offset = 2048,                 // 偏移 (12位DAC中心值)
        .phase = 0,                     // 相位
        .wave_type = DDS_WAVE_SINE,     // 正弦波
        .sample_rate = 1000000,         // 1MHz采样率，支持高精度
        .enable_interpolation = true,   // 启用插值提高精度
        .enable_modulation = false      // 不启用调制
    };

    DDS_Init(&dds_config);  // 初始化DDS
    DDS_Start();            // 启动DDS输出

    /* 频率测试序列 - 每10秒切换一次频率，便于示波器观察 */
    uint32_t test_frequencies[] = {
        1000,     // 1kHz    - 10秒
        10000,    // 10kHz   - 10秒
        50000,    // 50kHz   - 10秒
        100000,   // 100kHz  - 10秒
        200000,   // 200kHz  - 10秒
        500000,   // 500kHz  - 10秒
        1000000   // 1MHz    - 10秒 (满足不小于1MHz要求)
    };
    uint8_t freq_count = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    uint8_t current_freq_index = 0;
    uint32_t last_freq_change_time = 0;

    /* 主循环 - 分层频率显示 */
    while (1)
    {
        /* 检查是否需要切换频率 (每10秒切换) */
        uint32_t current_time = SysTick_GetTick();
        if (current_time - last_freq_change_time >= 10000) {  // 10000ms = 10秒
            // 切换到下一个频率
            DDS_SetFrequency(test_frequencies[current_freq_index]);

            // 更新索引，循环切换
            current_freq_index = (current_freq_index + 1) % freq_count;
            last_freq_change_time = current_time;
        }

        /* DDS自动运行，不需要手动更新 */
        // DDS模块通过定时器中断自动更新输出

        /* 适当延时，降低CPU占用 */
        Delay_ms(10);  // 10ms延时，DDS在后台自动运行
    }
}

// ==================== DDS高精度正弦波生成 ====================

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

/**
  * @brief  TIM6中断处理函数内部实现 (DDS更新)
  * @param  None
  * @retval None
  */
void DDS_TIM6_IRQHandler_Internal(void)
{
    // DDS定时器中断处理，生成高精度正弦波
    uint16_t dac_value = DDS_GetCurrentOutput();
    DAC8552_Write(DAC8552_CHANNEL_A, dac_value);
}

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


