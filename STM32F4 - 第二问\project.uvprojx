<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060528::V5.06 update 5 (build 528)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407VG</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.1.0.8</PackID>
          <PackURL>http://www.keil.com/pack</PackURL>
          <Cpu>IRAM(0x20000000,0x20000) IRAM2(0x10000000,0x10000) IROM(0x08000000,0x100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_1024 -********** -********* -FP0($$Device:STM32F407VG$Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F407VG$Device\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407VG$SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>project</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>0</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>STM32F40_41xxx,USE_STDPERIPH_DRIVER,ARM_MATH_CM4, ARM_MATH_MATRIX_CHECK, ARM_MATH_ROUNDING</Define>
              <Undefine></Undefine>
              <IncludePath>.\User;.\Start;.\Library;.\HardWare;.\Modules;.\Modules\Acquisition;.\Modules\Core;.\Modules\Generation;.\Modules\Interface;.\Modules\Processing</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Start</GroupName>
          <Files>
            <File>
              <FileName>arm_common_tables.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\arm_common_tables.h</FilePath>
            </File>
            <File>
              <FileName>arm_const_structs.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\arm_const_structs.h</FilePath>
            </File>
            <File>
              <FileName>arm_math.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\arm_math.h</FilePath>
            </File>
            <File>
              <FileName>core_cm4.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_cm4.h</FilePath>
            </File>
            <File>
              <FileName>core_cmFunc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_cmFunc.h</FilePath>
            </File>
            <File>
              <FileName>core_cmInstr.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_cmInstr.h</FilePath>
            </File>
            <File>
              <FileName>core_cmSimd.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_cmSimd.h</FilePath>
            </File>
            <File>
              <FileName>core_sc000.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_sc000.h</FilePath>
            </File>
            <File>
              <FileName>core_sc300.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\core_sc300.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f40_41xxx.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\Start\startup_stm32f40_41xxx.s</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\stm32f4xx.h</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Start\system_stm32f4xx.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\User\main.c</FilePath>
            </File>
            <File>
              <FileName>main.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\User\main.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\User\stm32f4xx_conf.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\User\stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\User\stm32f4xx_it.h</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\User\system_stm32f4xx.c</FilePath>
            </File>
            <File>
              <FileName>bsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\User\bsp.c</FilePath>
            </File>
            <File>
              <FileName>bsp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\User\bsp.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Library</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\misc.c</FilePath>
            </File>
            <File>
              <FileName>misc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\misc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_adc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_can.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_can.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_can.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_cec.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cec.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_cec.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_crc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_crc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cryp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_cryp.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cryp_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_cryp_aes.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cryp_des.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_cryp_des.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_cryp_tdes.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_cryp_tdes.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dac.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dac.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dbgmcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dbgmcu.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dbgmcu.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dbgmcu.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dcmi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dcmi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dcmi.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dcmi.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dfsdm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dfsdm.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dfsdm.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dfsdm.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dma.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma2d.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dma2d.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_dsi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dsi.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_dsi.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_exti.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_exti.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_flash.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash_ramfunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_flash_ramfunc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash_ramfunc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_flash_ramfunc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_fmpi2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_fmpi2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_fmpi2c.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_fmpi2c.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_fsmc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_fsmc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_gpio.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_hash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hash.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_hash.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hash_md5.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_hash_md5.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hash_sha1.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_hash_sha1.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_i2c.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_i2c.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_iwdg.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_iwdg.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_lptim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_lptim.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_lptim.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_ltdc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_ltdc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_pwr.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_pwr.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_qspi.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_qspi.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rcc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_rcc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rng.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_rng.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rtc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_rtc.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_sai.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_sai.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_sai.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_sai.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_sdio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_sdio.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_sdio.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spdifrx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_spdifrx.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spdifrx.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_spdifrx.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_spi.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_syscfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_syscfg.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_tim.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_tim.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_usart.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_usart.h</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Library\stm32f4xx_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_wwdg.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Library\stm32f4xx_wwdg.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Modules</GroupName>
          <Files>
            <File>
              <FileName>adc_dma.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Acquisition\adc_dma.h</FilePath>
            </File>
            <File>
              <FileName>parallel_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Acquisition\parallel_adc.h</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Modules\Core\systick.c</FilePath>
            </File>
            <File>
              <FileName>systick.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Core\systick.h</FilePath>
            </File>
            <File>
              <FileName>key.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Interface\key.h</FilePath>
            </File>
            <File>
              <FileName>oled.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Interface\oled.h</FilePath>
            </File>
            <File>
              <FileName>fft.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Processing\fft.h</FilePath>
            </File>
            <File>
              <FileName>dac8552.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Modules\Generation\dac8552.c</FilePath>
            </File>
            <File>
              <FileName>dac8552.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Generation\dac8552.h</FilePath>
            </File>
            <File>
              <FileName>ad7606.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Acquisition\ad7606.h</FilePath>
            </File>
            <File>
              <FileName>cd4052.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\Modules\Interface\cd4052.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.0.1" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
      <component Cclass="CMSIS" Cgroup="DSP" Cvendor="ARM" Cversion="1.5.1" condition="CMSIS DSP">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>
