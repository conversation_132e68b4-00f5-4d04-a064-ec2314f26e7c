/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：DAC8552正弦信号生成
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// 正弦波生成参数
typedef struct {
    uint32_t frequency;      // 频率 (Hz)
    float amplitude;         // 幅度 (V)
    uint32_t phase_acc;      // 相位累加器
    uint32_t phase_inc;      // 相位增量
} SineGen_t;

SineGen_t g_sine_gen;

// 正弦波查找表 (256点)
const uint16_t sine_table[256] = {
    2048, 2098, 2148, 2198, 2248, 2298, 2348, 2398, 2447, 2496, 2545, 2594, 2642, 2690, 2737, 2784,
    2831, 2877, 2923, 2968, 3013, 3057, 3100, 3143, 3185, 3226, 3267, 3307, 3346, 3385, 3423, 3459,
    3495, 3530, 3565, 3598, 3630, 3662, 3692, 3722, 3750, 3777, 3804, 3829, 3853, 3876, 3898, 3919,
    3939, 3958, 3976, 3992, 4007, 4021, 4034, 4045, 4056, 4065, 4073, 4080, 4085, 4089, 4093, 4094,
    4095, 4094, 4093, 4089, 4085, 4080, 4073, 4065, 4056, 4045, 4034, 4021, 4007, 3992, 3976, 3958,
    3939, 3919, 3898, 3876, 3853, 3829, 3804, 3777, 3750, 3722, 3692, 3662, 3630, 3598, 3565, 3530,
    3495, 3459, 3423, 3385, 3346, 3307, 3267, 3226, 3185, 3143, 3100, 3057, 3013, 2968, 2923, 2877,
    2831, 2784, 2737, 2690, 2642, 2594, 2545, 2496, 2447, 2398, 2348, 2298, 2248, 2198, 2148, 2098,
    2048, 1998, 1948, 1898, 1848, 1798, 1748, 1698, 1649, 1600, 1551, 1502, 1454, 1406, 1359, 1312,
    1265, 1219, 1173, 1128, 1083, 1039, 996, 953, 911, 870, 829, 789, 750, 711, 673, 637,
    601, 566, 531, 498, 466, 434, 404, 374, 346, 319, 292, 267, 243, 220, 198, 177,
    157, 138, 120, 104, 89, 75, 62, 51, 40, 31, 23, 16, 11, 7, 3, 2,
    1, 2, 3, 7, 11, 16, 23, 31, 40, 51, 62, 75, 89, 104, 120, 138,
    157, 177, 198, 220, 243, 267, 292, 319, 346, 374, 404, 434, 466, 498, 531, 566,
    601, 637, 673, 711, 750, 789, 829, 870, 911, 953, 996, 1039, 1083, 1128, 1173, 1219,
    1265, 1312, 1359, 1406, 1454, 1502, 1551, 1600, 1649, 1698, 1748, 1798, 1848, 1898, 1948, 1998
};

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void SineGen_Init(void);
void SineGen_SetFrequency(uint32_t freq);
void SineGen_SetAmplitude(float amp);
uint16_t SineGen_GetSample(void);
void SineGen_Update(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* 初始化DAC8552和正弦波生成器 */
    DAC8552_Init();  // 简化，不检查返回值
    SineGen_Init();

    /* 设置初始参数 */
    SineGen_SetFrequency(1000);  // 1kHz
    SineGen_SetAmplitude(2.5f);  // 2.5V

    /* 频率测试序列 - 每3秒切换一次频率，覆盖到1MHz */
    uint32_t test_frequencies[] = {
        1000,     // 1kHz    - 3秒
        10000,    // 10kHz   - 3秒
        50000,    // 50kHz   - 3秒
        100000,   // 100kHz  - 3秒
        200000,   // 200kHz  - 3秒
        500000,   // 500kHz  - 3秒
        1000000   // 1MHz    - 3秒 (满足不小于1MHz要求)
    };
    uint8_t freq_count = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    uint8_t current_freq_index = 0;
    uint32_t last_freq_change_time = 0;

    /* 主循环 - 分层频率显示 */
    while (1)
    {
        /* 检查是否需要切换频率 (每3秒切换) */
        uint32_t current_time = SysTick_GetTick();
        if (current_time - last_freq_change_time >= 3000) {  // 3000ms = 3秒
            // 切换到下一个频率
            SineGen_SetFrequency(test_frequencies[current_freq_index]);

            // 更新索引，循环切换
            current_freq_index = (current_freq_index + 1) % freq_count;
            last_freq_change_time = current_time;
        }

        /* 持续更新正弦波输出 */
        SineGen_Update();

        /* 极短延时，支持高频输出(1MHz需要高更新率) */
        // 对于1MHz信号，需要尽可能快的更新速度
        // Delay_ms(1);  // 注释掉延时，最大化输出频率
    }
}

// ==================== 正弦波生成函数实现 ====================

/**
 * @brief  正弦波生成器初始化
 * @param  None
 * @retval None
 */
void SineGen_Init(void)
{
    // 初始化正弦波生成器参数
    g_sine_gen.frequency = 1000;        // 默认1kHz
    g_sine_gen.amplitude = 2.5f;        // 默认2.5V
    g_sine_gen.phase_acc = 0;

    // 计算相位增量 (24位精度)
    // phase_inc = (frequency * 2^24) / sample_rate
    // 采样率设为10MHz，支持高达1MHz的正弦波生成
    g_sine_gen.phase_inc = (uint32_t)((uint64_t)g_sine_gen.frequency * 16777216ULL / 10000000ULL);
}

/**
 * @brief  设置正弦波频率
 * @param  freq: 目标频率 (Hz)
 * @retval None
 */
void SineGen_SetFrequency(uint32_t freq)
{
    // 频率范围限制 (100Hz - 1MHz)
    if (freq < 100) freq = 100;
    if (freq > 1000000) freq = 1000000;

    g_sine_gen.frequency = freq;

    // 重新计算相位增量 (提高采样率到10MHz以支持1MHz正弦波)
    // phase_inc = (frequency * 2^24) / sample_rate
    // 采样率设为10MHz，确保1MHz正弦波有足够的采样点
    g_sine_gen.phase_inc = (uint32_t)((uint64_t)freq * 16777216ULL / 10000000ULL);
}

/**
 * @brief  设置正弦波幅度
 * @param  amp: 目标幅度 (V)
 * @retval None
 */
void SineGen_SetAmplitude(float amp)
{
    // 幅度范围限制 (0.1V - 4.5V)
    if (amp < 0.1f) amp = 0.1f;
    if (amp > 4.5f) amp = 4.5f;

    g_sine_gen.amplitude = amp;
}

/**
 * @brief  生成正弦波采样点
 * @param  None
 * @retval 16位DAC数值
 */
uint16_t SineGen_GetSample(void)
{
    uint16_t dac_value;
    uint8_t table_index;
    
    // 获取正弦波表索引 (使用高8位)
    table_index = (g_sine_gen.phase_acc >> 16) & 0xFF;
    
    // 从查找表获取基础值 (0-4095)
    uint16_t base_value = sine_table[table_index];
    
    // 应用幅度缩放
    float scaled_value = (float)base_value * g_sine_gen.amplitude / 5.0f;
    
    // 转换为DAC数值 (0-65535)
    dac_value = (uint16_t)(scaled_value * 16.0f);
    
    // 更新相位累加器
    g_sine_gen.phase_acc += g_sine_gen.phase_inc;
    
    return dac_value;
}

/**
 * @brief  更新正弦波输出
 * @param  None
 * @retval None
 */
void SineGen_Update(void)
{
    // 生成新的采样点并输出到DAC
    uint16_t dac_sample = SineGen_GetSample();
    DAC8552_Write(DAC8552_CHANNEL_A, dac_sample);
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif


