<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Systick Timer (SYSTICK)</title>
<title>CMSIS-CORE: Systick Timer (SYSTICK)</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___sys_tick__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Systick Timer (SYSTICK)</div>  </div>
</div><!--header-->
<div class="contents">

<p>Initialize and start the SysTick timer.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gabe47de40e9b0ad465b752297a9d9f427"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427">SysTick_Config</a> (uint32_t ticks)</td></tr>
<tr class="memdesc:gabe47de40e9b0ad465b752297a9d9f427"><td class="mdescLeft">&#160;</td><td class="mdescRight">System Tick Timer Configuration.  <a href="#gabe47de40e9b0ad465b752297a9d9f427"></a><br/></td></tr>
<tr class="separator:gabe47de40e9b0ad465b752297a9d9f427"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<pre class="fragment">The System Tick Time (SysTick) generates interrupt requests on a regular basis.
This allows an OS to carry out context switching to support multiple tasking. For applications
that do not require an OS, the SysTick can be used for time keeping, time measurement, or as an 
interrupt source for tasks that need to be executed regularly.
</pre><h1><a class="anchor" id="SysTick_code_ex_sec"></a>
Code Example</h1>
<p>The code below shows the usage of the function <a class="el" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427" title="System Tick Timer Configuration.">SysTick_Config()</a> with an LPC1700.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;LPC17xx.h&quot;</span></div>
<div class="line"></div>
<div class="line">uint32_t msTicks = 0;                                       <span class="comment">/* Variable to store millisecond ticks */</span></div>
<div class="line"></div>
<div class="line">                                            </div>
<div class="line"><span class="keywordtype">void</span> SysTick_Handler(<span class="keywordtype">void</span>)  {                               <span class="comment">/* SysTick interrupt Handler.</span></div>
<div class="line"><span class="comment">  msTicks++;                                                   See startup file startup_LPC17xx.s for SysTick vector */</span> </div>
<div class="line">}</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="keywordtype">int</span> main (<span class="keywordtype">void</span>)  {</div>
<div class="line">  uint32_t returnCode;</div>
<div class="line"></div>
<div class="line">  returnCode = <a class="code" href="group___sys_tick__gr.html#gabe47de40e9b0ad465b752297a9d9f427" title="System Tick Timer Configuration.">SysTick_Config</a>(<a class="code" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6" title="Variable to hold the system core clock value.">SystemCoreClock</a> / 1000);      <span class="comment">/* Configure SysTick to generate an interrupt every millisecond */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (returnCode != 0)  {                                   <span class="comment">/* Check return code for errors */</span></div>
<div class="line">    <span class="comment">// Error Handling </span></div>
<div class="line">  }</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);</div>
<div class="line">}</div>
</div><!-- fragment --> <h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gabe47de40e9b0ad465b752297a9d9f427"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t SysTick_Config </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ticks</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initialises and starts the System Tick Timer and its interrupt. After this call, the SysTick timer creates interrupts with the specified time interval. Counter is in free running mode to generate periodical interrupts.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ticks</td><td>Number of ticks between two interrupts</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 - success </dd>
<dd>
1 - failure</dd></dl>
<dl class="section note"><dt>Note</dt><dd>When <b>#define __Vendor_SysTickConfig</b> is set to 1, the standard function <b>SysTick_Config</b> is excluded. In this case, the file <b><em>device</em>.h</b> must contain a vendor specific implementation of this function. </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
