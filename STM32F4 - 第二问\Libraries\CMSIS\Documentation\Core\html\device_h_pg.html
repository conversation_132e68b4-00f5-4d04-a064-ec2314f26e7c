<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Device Header File &lt;device.h&gt;</title>
<title>CMSIS-CORE: Device Header File &lt;device.h&gt;</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('device_h_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Device Header File &lt;device.h&gt; </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> contains the following sections that are device specific:</p>
<ul>
<li><a class="el" href="device_h_pg.html#interrupt_number_sec">Interrupt Number Definition</a> provides interrupt numbers (IRQn) for all exceptions and interrupts of the device.</li>
<li><a class="el" href="device_h_pg.html#core_config_sect">Configuration of the Processor and Core Peripherals</a> reflect the features of the device.</li>
<li><a class="el" href="device_h_pg.html#device_access">Device Peripheral Access Layer</a> provides definitions for the <a class="el" href="group__peripheral__gr.html">Peripheral Access</a> to all device peripherals. It contains all data structures and the address mapping for device-specific peripherals.</li>
<li><b>Access Functions for Peripherals (optional)</b> provide additional helper functions for peripherals that are useful for programming of these peripherals. Access Functions may be provided as inline functions or can be extern references to a device-specific library provided by the silicon vendor.</li>
</ul>
<p><a href="Modules.html"><b>Reference</b> </a> describes the standard features and functions of the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> in detail.</p>
<h1><a class="anchor" id="interrupt_number_sec"></a>
Interrupt Number Definition</h1>
<p><a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> contains the enumeration <a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> that defines all exceptions and interrupts of the device.</p>
<ul>
<li>Negative IRQn values represent processor core exceptions (internal interrupts).</li>
<li>Positive IRQn values represent device-specific exceptions (external interrupts). The first device-specific interrupt has the IRQn value 0. The IRQn values needs extension to reflect the device-specific interrupt vector table in the <a class="el" href="startup_s_pg.html">Startup File startup_&lt;device&gt;.s</a>.</li>
</ul>
<p><b>Example:</b> </p>
<p>The following example shows the extension of the interrupt vector table for the LPC1100 device family.</p>
<div class="fragment"><div class="line"><span class="keyword">typedef</span> <span class="keyword">enum</span> IRQn</div>
<div class="line">{</div>
<div class="line"><span class="comment">/******  Cortex-M0 Processor Exceptions Numbers ***************************************************/</span></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ade177d9c70c89e084093024b932a4e30" title="Exception 2: Non Maskable Interrupt.">NonMaskableInt_IRQn</a>           = -14,      </div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ab1a222a34a32f0ef5ac65e714efc1f85" title="Exception 3: Hard Fault Interrupt.">HardFault_IRQn</a>                = -13,      </div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a4ce820b3cc6cf3a796b41aadc0cf1237" title="Exception 11: SV Call Interrupt.">SVCall_IRQn</a>                   = -5,       </div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a03c3cc89984928816d81793fc7bce4a2" title="Exception 14: Pend SV Interrupt [not on Cortex-M0 variants].">PendSV_IRQn</a>                   = -2,       </div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6dbff8f8543325f3474cbae2446776e7" title="Exception 15: System Tick Interrupt.">SysTick_IRQn</a>                  = -1,       </div>
<div class="line"><span class="comment">/******  LPC11xx/LPC11Cxx Specific Interrupt Numbers **********************************************/</span></div>
<div class="line">  WAKEUP0_IRQn                  = 0,        </div>
<div class="line">  WAKEUP1_IRQn                  = 1,        </div>
<div class="line">  WAKEUP2_IRQn                  = 2,</div>
<div class="line">                 :       :</div>
<div class="line">                 :       :</div>
<div class="line">  EINT1_IRQn                    = 30,       </div>
<div class="line">  EINT0_IRQn                    = 31,       </div>
<div class="line">} <a class="code" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8" title="Definition of IRQn numbers.">IRQn_Type</a>;</div>
</div><!-- fragment --><h1><a class="anchor" id="core_config_sect"></a>
Configuration of the Processor and Core Peripherals</h1>
<p>The <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> configures the Cortex-M or SecurCore processor and the core peripherals with <em>#defines</em> that are set prior to including the file <b>core_&lt;cpu&gt;.h</b>.</p>
<p>The following tables list the <em>#defines</em> along with the possible values for each processor core. If these <em>#defines</em> are missing default values are used.</p>
<p><b>core_cm0.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__CM0_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 </td><td>2 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>core_cm0plus.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__CM0PLUS_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 </td><td>2 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>core_cm3.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__CM3_REV </td><td>0x0101 | 0x0200 </td><td>0x0200 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 .. 8 </td><td>4 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__MPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a MPU is present or not  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>core_cm4.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__CM4_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 .. 8 </td><td>4 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__MPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a MPU is present or not  </td></tr>
<tr>
<td>__FPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a FPU is present or not  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>core_cm7.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__CM7_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__MPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a MPU is present or not  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 .. 8 </td><td>4 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
<tr>
<td>__FPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a FPU is present or not. See <b>__FPU_DP</b> description below.  </td></tr>
<tr>
<td>__FPU_DP </td><td>0 .. 1 </td><td>0 </td><td>The combination of the defines <b>__FPU_PRESENT</b> and <b>__FPU_DP</b> determine the whether the FPU is with single or double precision as shown in the table below. <br/>
<br/>
 <table  class="cmtable">
<tr bgcolor="cyan">
<td><b>__FPU_PRESENT</b> </td><td><b>__FPU_DP</b> </td><td><b>Description</b>  </td></tr>
<tr>
<td align="center">0 </td><td align="center"><em>ignored</em> </td><td>Processor has no FPU. The value set for <b>__FPU_DP</b> has no influence.   </td></tr>
<tr>
<td align="center">1 </td><td align="center">0 </td><td>Processor with FPU with single precision. The file <b>ARMCM7_SP.h</b> has preconfigured settings for this combination.  </td></tr>
<tr>
<td align="center">1 </td><td align="center">1 </td><td>Processor with FPU with double precision. The file <b>ARMCM7_DP.h</b> has preconfigured settings for this combination.  </td></tr>
</table>
</td></tr>
<tr>
<td>__ICACHE_PRESENT </td><td>0 .. 1 </td><td>1 </td><td>Instruction Chache present or not  </td></tr>
<tr>
<td>__DCACHE_PRESENT </td><td>0 .. 1 </td><td>1 </td><td>Data Chache present or not  </td></tr>
<tr>
<td>__DTCM_PRESENT </td><td>0 .. 1 </td><td>1 </td><td><p class="starttd">Data Tightly Coupled Memory is present or not </p>
<p class="endtd"></p>
</td></tr>
</table>
<p><b>core_sc000.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__SC000_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 </td><td>2 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__MPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a MPU is present or not  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>core_sc300.h</b> </p>
<table  class="cmtable">
<tr>
<th>#define </th><th>Value Range </th><th>Default </th><th>Description  </th></tr>
<tr>
<td>__SC300_REV </td><td>0x0000 </td><td>0x0000 </td><td>Core revision number ([15:8] revision number, [7:0] patch number)  </td></tr>
<tr>
<td>__NVIC_PRIO_BITS </td><td>2 .. 8 </td><td>4 </td><td>Number of priority bits implemented in the NVIC (device specific)  </td></tr>
<tr>
<td>__MPU_PRESENT </td><td>0 .. 1 </td><td>0 </td><td>Defines if a MPU is present or not  </td></tr>
<tr>
<td>__Vendor_SysTickConfig </td><td>0 .. 1 </td><td>0 </td><td>If this define is set to 1, then the default <b>SysTick_Config</b> function is excluded. In this case, the file <em><b>device.h</b></em> must contain a vendor specific implementation of this function.  </td></tr>
</table>
<p><b>Example</b> </p>
<p>The following code exemplifies the configuration of the Cortex-M4 Processor and Core Peripherals.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM4_REV                 0x0001    </span><span class="comment">/* Core revision r0p1                                 */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __MPU_PRESENT             1         </span><span class="comment">/* MPU present or not                                 */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __NVIC_PRIO_BITS          3         </span><span class="comment">/* Number of Bits used for Priority Levels            */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __Vendor_SysTickConfig    0         </span><span class="comment">/* Set to 1 if different SysTick Config is used       */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __FPU_PRESENT             1         </span><span class="comment">/* FPU present or not                                 */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>.</div>
<div class="line">.</div>
<div class="line"><span class="preprocessor">#include &lt;core_cm4.h&gt;</span>                       <span class="comment">/* Cortex-M4 processor and core peripherals           */</span></div>
</div><!-- fragment --><h1><a class="anchor" id="core_version_sect"></a>
CMSIS Version and Processor Information</h1>
<p>Defines in the core_<em>cpu</em>.h file identify the version of the CMSIS-CORE and the processor used. The following shows the defines in the various core_<em>cpu</em>.h files that may be used in the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> to verify a minimum version or ensure that the right processor core is used.</p>
<p><b>core_cm0.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM0_CMSIS_VERSION_MAIN    (0x03)                                   </span><span class="comment">/* [31:16] CMSIS HAL main version   */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM0_CMSIS_VERSION_SUB     (0x00)                                   </span><span class="comment">/* [15:0]  CMSIS HAL sub version    */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM0_CMSIS_VERSION         ((__CM0_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                      __CM0_CMSIS_VERSION_SUB          )     </span><span class="comment">/* CMSIS HAL version number         */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_M                  (0x00)                                   </span><span class="comment">/* Cortex-M Core                    */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p><b>core_cm0plus.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM0PLUS_CMSIS_VERSION_MAIN   (0x03)                                </span><span class="comment">/* [31:16] CMSIS HAL main version   */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM0PLUS_CMSIS_VERSION_SUB    (0x00)                                </span><span class="comment">/* [15:0]  CMSIS HAL sub version    */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM0PLUS_CMSIS_VERSION        ((__CM0P_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                     __CM0P_CMSIS_VERSION_SUB          )  </span><span class="comment">/* CMSIS HAL version number         */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_M                  (0x00)                                </span><span class="comment">/* Cortex-M Core                    */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p><b>core_cm3.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM3_CMSIS_VERSION_MAIN    (0x03)                                   </span><span class="comment">/* [31:16] CMSIS HAL main version   */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM3_CMSIS_VERSION_SUB     (0x00)                                   </span><span class="comment">/* [15:0]  CMSIS HAL sub version    */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM3_CMSIS_VERSION         ((__CM3_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                      __CM3_CMSIS_VERSION_SUB          )     </span><span class="comment">/* CMSIS HAL version number         */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_M                  (0x03)                                   </span><span class="comment">/* Cortex-M Core                    */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p><b>core_cm4.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM4_CMSIS_VERSION_MAIN    (0x04)                                   </span><span class="comment">/* [31:16] CMSIS HAL main version   */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM4_CMSIS_VERSION_SUB     (0x00)                                   </span><span class="comment">/* [15:0]  CMSIS HAL sub version    */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __CM4_CMSIS_VERSION         ((__CM4_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                      __CM4_CMSIS_VERSION_SUB          )     </span><span class="comment">/* CMSIS HAL version number         */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_M                  (0x04)                                   </span><span class="comment">/* Cortex-M Core                    */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p><b>core_cm7.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __CM7_CMSIS_VERSION_MAIN  (0x04)                                     </span></div>
<div class="line"><span class="preprocessor">#define __CM7_CMSIS_VERSION_SUB   (0x00)                                     </span></div>
<div class="line"><span class="preprocessor">#define __CM7_CMSIS_VERSION       ((__CM7_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                    __CM7_CMSIS_VERSION_SUB          )       </span></div>
<div class="line"><span class="preprocessor">#define __CORTEX_M                (0x07)                                     </span></div>
</div><!-- fragment --><p><b>core_sc000.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __SC000_CMSIS_VERSION_MAIN  (0x04)                                   </span><span class="comment">/* [31:16] CMSIS HAL main version */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __SC000_CMSIS_VERSION_SUB   (0x00)                                   </span><span class="comment">/* [15:0]  CMSIS HAL sub version  */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __SC000_CMSIS_VERSION       ((__SC000_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                      __SC000_CMSIS_VERSION_SUB          )   </span><span class="comment">/* CMSIS HAL version number       */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_SC                 (000)                                    </span><span class="comment">/* Cortex secure core             */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p><b>core_sc300.h</b> </p>
<div class="fragment"><div class="line"><span class="preprocessor">#define __SC300_CMSIS_VERSION_MAIN  (0x04)                                   </span><span class="comment">/* [31:16] CMSIS HAL main version */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __SC300_CMSIS_VERSION_SUB   (0x00)                                   </span><span class="comment">/* [15:0]  CMSIS HAL sub version  */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define __SC300_CMSIS_VERSION       ((__SC300_CMSIS_VERSION_MAIN &lt;&lt; 16) | \</span></div>
<div class="line"><span class="preprocessor">                                      __SC300_CMSIS_VERSION_SUB          )   </span><span class="comment">/* CMSIS HAL version number       */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span>...    </div>
<div class="line"><span class="preprocessor">#define __CORTEX_SC                 (300)                                    </span><span class="comment">/* Cortex secure core             */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><h1><a class="anchor" id="device_access"></a>
Device Peripheral Access Layer</h1>
<p>The <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> contains for each peripheral:</p>
<ul>
<li>Register Layout Typedef</li>
<li>Base Address</li>
<li>Access Definitions</li>
</ul>
<p>The section <a class="el" href="group__peripheral__gr.html">Peripheral Access</a> shows examples for peripheral definitions.</p>
<h1><a class="anchor" id="device_h_sec"></a>
Device.h Template File</h1>
<p>The silicon vendor needs to extend the Device.h template file with the CMSIS features described above. In addition the <a class="el" href="device_h_pg.html">Device Header File &lt;device.h&gt;</a> may contain functions to access device-specific peripherals. The <a class="el" href="system_c_pg.html#system_Device_h_sec">system_Device.h Template File</a> which is provided as part of the CMSIS specification is shown below.</p>
<pre class="fragment">/**************************************************************************//**
 * @file     &lt;Device&gt;.h
 * @brief    CMSIS Cortex-M# Core Peripheral Access Layer Header File for
 *           Device &lt;Device&gt;
 * @version  V3.10
 * @date     23. November 2012
 *
 * @note
 *
 ******************************************************************************/
/* Copyright (c) 2012 ARM LIMITED

   All rights reserved.
   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions are met:
   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.
   - Neither the name of ARM nor the names of its contributors may be used
     to endorse or promote products derived from this software without
     specific prior written permission.
   *
   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE
   LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   POSSIBILITY OF SUCH DAMAGE.
   ---------------------------------------------------------------------------*/


#ifndef &lt;Device&gt;_H      /* ToDo: replace '&lt;Device&gt;' with your device name */
#define &lt;Device&gt;_H

#ifdef __cplusplus
 extern "C" {
#endif

/* ToDo: replace '&lt;Device&gt;' with your device name; add your doxyGen comment   */
/** @addtogroup &lt;Device&gt;_Definitions &lt;Device&gt; Definitions
  This file defines all structures and symbols for &lt;Device&gt;:
    - registers and bitfields
    - peripheral base address
    - peripheral ID
    - Peripheral definitions
  @{
*/


/******************************************************************************/
/*                Processor and Core Peripherals                              */
/******************************************************************************/
/** @addtogroup &lt;Device&gt;_CMSIS Device CMSIS Definitions
  Configuration of the Cortex-M# Processor and Core Peripherals
  @{
*/

/*
 * ==========================================================================
 * ---------- Interrupt Number Definition -----------------------------------
 * ==========================================================================
 */

typedef enum IRQn
{
/******  Cortex-M# Processor Exceptions Numbers ***************************************************/

/* ToDo: use this Cortex interrupt numbers if your device is a CORTEX-M0 device                   */
  NonMaskableInt_IRQn           = -14,      /*!&lt;  2 Non Maskable Interrupt                        */
  HardFault_IRQn                = -13,      /*!&lt;  3 Hard Fault Interrupt                          */
  SVCall_IRQn                   = -5,       /*!&lt; 11 SV Call Interrupt                             */
  PendSV_IRQn                   = -2,       /*!&lt; 14 Pend SV Interrupt                             */
  SysTick_IRQn                  = -1,       /*!&lt; 15 System Tick Interrupt                         */

/* ToDo: use this Cortex interrupt numbers if your device is a CORTEX-M3 / Cortex-M4 device       */
  NonMaskableInt_IRQn           = -14,      /*!&lt;  2 Non Maskable Interrupt                        */
  MemoryManagement_IRQn         = -12,      /*!&lt;  4 Memory Management Interrupt                   */
  BusFault_IRQn                 = -11,      /*!&lt;  5 Bus Fault Interrupt                           */
  UsageFault_IRQn               = -10,      /*!&lt;  6 Usage Fault Interrupt                         */
  SVCall_IRQn                   = -5,       /*!&lt; 11 SV Call Interrupt                             */
  DebugMonitor_IRQn             = -4,       /*!&lt; 12 Debug Monitor Interrupt                       */
  PendSV_IRQn                   = -2,       /*!&lt; 14 Pend SV Interrupt                             */
  SysTick_IRQn                  = -1,       /*!&lt; 15 System Tick Interrupt                         */

/******  Device Specific Interrupt Numbers ********************************************************/
/* ToDo: add here your device specific external interrupt numbers
         according the interrupt handlers defined in startup_Device.s
         eg.: Interrupt for Timer#1       TIM1_IRQHandler   -&gt;   TIM1_IRQn                        */
  &lt;DeviceInterrupt&gt;_IRQn        = 0,        /*!&lt; Device Interrupt                                 */
} IRQn_Type;


/*
 * ==========================================================================
 * ----------- Processor and Core Peripheral Section ------------------------
 * ==========================================================================
 */

/* Configuration of the Cortex-M# Processor and Core Peripherals */
/* ToDo: set the defines according your Device                                                    */
/* ToDo: define the correct core revision
         __CM0_REV if your device is a CORTEX-M0 device
         __CM3_REV if your device is a CORTEX-M3 device
         __CM4_REV if your device is a CORTEX-M4 device                                           */
#define __CM#_REV                 0x0201    /*!&lt; Core Revision r2p1                               */
#define __NVIC_PRIO_BITS          2         /*!&lt; Number of Bits used for Priority Levels          */
#define __Vendor_SysTickConfig    0         /*!&lt; Set to 1 if different SysTick Config is used     */
#define __MPU_PRESENT             0         /*!&lt; MPU present or not                               */
/* ToDo: define __FPU_PRESENT if your devise is a CORTEX-M4                                       */
#define __FPU_PRESENT             0        /*!&lt; FPU present or not                                */

/*@}*/ /* end of group &lt;Device&gt;_CMSIS */


/* ToDo: include the correct core_cm#.h file
         core_cm0.h if your device is a CORTEX-M0 device
         core_cm3.h if your device is a CORTEX-M3 device
         core_cm4.h if your device is a CORTEX-M4 device                                          */
#include &lt;core_cm#.h&gt;                       /* Cortex-M# processor and core peripherals           */
/* ToDo: include your system_&lt;Device&gt;.h file
         replace '&lt;Device&gt;' with your device name                                                 */
#include "system_&lt;Device&gt;.h"                /* &lt;Device&gt; System  include file                      */


/******************************************************************************/
/*                Device Specific Peripheral registers structures             */
/******************************************************************************/
/** @addtogroup &lt;Device&gt;_Peripherals &lt;Device&gt; Peripherals
  &lt;Device&gt; Device Specific Peripheral registers structures
  @{
*/

#if defined ( __CC_ARM   )
#pragma anon_unions
#endif

/* ToDo: add here your device specific peripheral access structure typedefs
         following is an example for a timer                                  */

/*------------- 16-bit Timer/Event Counter (TMR) -----------------------------*/
/** @addtogroup &lt;Device&gt;_TMR &lt;Device&gt; 16-bit Timer/Event Counter (TMR)
  @{
*/
typedef struct
{
  __IO uint32_t EN;                         /*!&lt; Offset: 0x0000   Timer Enable Register           */
  __IO uint32_t RUN;                        /*!&lt; Offset: 0x0004   Timer RUN Register              */
  __IO uint32_t CR;                         /*!&lt; Offset: 0x0008   Timer Control Register          */
  __IO uint32_t MOD;                        /*!&lt; Offset: 0x000C   Timer Mode Register             */
       uint32_t RESERVED0[1];
  __IO uint32_t ST;                         /*!&lt; Offset: 0x0014   Timer Status Register           */
  __IO uint32_t IM;                         /*!&lt; Offset: 0x0018   Interrupt Mask Register         */
  __IO uint32_t UC;                         /*!&lt; Offset: 0x001C   Timer Up Counter Register       */
  __IO uint32_t RG0                         /*!&lt; Offset: 0x0020   Timer Register                  */
       uint32_t RESERVED1[2];
  __IO uint32_t CP;                         /*!&lt; Offset: 0x002C   Capture register                */
} &lt;DeviceAbbreviation&gt;_TMR_TypeDef;
/*@}*/ /* end of group &lt;Device&gt;_TMR */


#if defined ( __CC_ARM   )
#pragma no_anon_unions
#endif

/*@}*/ /* end of group &lt;Device&gt;_Peripherals */


/******************************************************************************/
/*                         Peripheral memory map                              */
/******************************************************************************/
/* ToDo: add here your device peripherals base addresses
         following is an example for timer                                    */
/** @addtogroup &lt;Device&gt;_MemoryMap &lt;Device&gt; Memory Mapping
  @{
*/

/* Peripheral and SRAM base address */
#define &lt;DeviceAbbreviation&gt;_FLASH_BASE       (0x00000000UL)                              /*!&lt; (FLASH     ) Base Address */
#define &lt;DeviceAbbreviation&gt;_SRAM_BASE        (0x20000000UL)                              /*!&lt; (SRAM      ) Base Address */
#define &lt;DeviceAbbreviation&gt;_PERIPH_BASE      (0x40000000UL)                              /*!&lt; (Peripheral) Base Address */

/* Peripheral memory map */
#define &lt;DeviceAbbreviation&gt;TIM0_BASE         (&lt;DeviceAbbreviation&gt;_PERIPH_BASE)          /*!&lt; (Timer0    ) Base Address */
#define &lt;DeviceAbbreviation&gt;TIM1_BASE         (&lt;DeviceAbbreviation&gt;_PERIPH_BASE + 0x0800) /*!&lt; (Timer1    ) Base Address */
#define &lt;DeviceAbbreviation&gt;TIM2_BASE         (&lt;DeviceAbbreviation&gt;_PERIPH_BASE + 0x1000) /*!&lt; (Timer2    ) Base Address */
/*@}*/ /* end of group &lt;Device&gt;_MemoryMap */


/******************************************************************************/
/*                         Peripheral declaration                             */
/******************************************************************************/
/* ToDo: add here your device peripherals pointer definitions
         following is an example for timer                                    */

/** @addtogroup &lt;Device&gt;_PeripheralDecl &lt;Device&gt; Peripheral Declaration
  @{
*/

#define &lt;DeviceAbbreviation&gt;_TIM0        ((&lt;DeviceAbbreviation&gt;_TMR_TypeDef *) &lt;DeviceAbbreviation&gt;TIM0_BASE)
#define &lt;DeviceAbbreviation&gt;_TIM1        ((&lt;DeviceAbbreviation&gt;_TMR_TypeDef *) &lt;DeviceAbbreviation&gt;TIM0_BASE)
#define &lt;DeviceAbbreviation&gt;_TIM2        ((&lt;DeviceAbbreviation&gt;_TMR_TypeDef *) &lt;DeviceAbbreviation&gt;TIM0_BASE)
/*@}*/ /* end of group &lt;Device&gt;_PeripheralDecl */

/*@}*/ /* end of group &lt;Device&gt;_Definitions */

#ifdef __cplusplus
}
#endif

#endif  /* &lt;Device&gt;_H */
</pre> </div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="_templates_pg.html">Template Files</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
