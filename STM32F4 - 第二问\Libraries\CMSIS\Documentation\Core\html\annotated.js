var annotated =
[
    [ "APSR_Type", "union_a_p_s_r___type.html", "union_a_p_s_r___type" ],
    [ "CONTROL_Type", "union_c_o_n_t_r_o_l___type.html", "union_c_o_n_t_r_o_l___type" ],
    [ "CoreDebug_Type", "struct_core_debug___type.html", "struct_core_debug___type" ],
    [ "DWT_Type", "struct_d_w_t___type.html", "struct_d_w_t___type" ],
    [ "FPU_Type", "struct_f_p_u___type.html", "struct_f_p_u___type" ],
    [ "IPSR_Type", "union_i_p_s_r___type.html", "union_i_p_s_r___type" ],
    [ "ITM_Type", "struct_i_t_m___type.html", "struct_i_t_m___type" ],
    [ "MPU_Type", "struct_m_p_u___type.html", "struct_m_p_u___type" ],
    [ "NVIC_Type", "struct_n_v_i_c___type.html", "struct_n_v_i_c___type" ],
    [ "SCB_Type", "struct_s_c_b___type.html", "struct_s_c_b___type" ],
    [ "SCnSCB_Type", "struct_s_cn_s_c_b___type.html", "struct_s_cn_s_c_b___type" ],
    [ "SysTick_Type", "struct_sys_tick___type.html", "struct_sys_tick___type" ],
    [ "TPI_Type", "struct_t_p_i___type.html", "struct_t_p_i___type" ],
    [ "xPSR_Type", "unionx_p_s_r___type.html", "unionx_p_s_r___type" ]
];