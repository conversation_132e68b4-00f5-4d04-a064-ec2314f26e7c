/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：DAC8552正弦信号生成
#include "../Modules/Generation/dac8552.h"     // DAC8552双通道DAC驱动

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* 初始化DAC8552 */
    DAC8552_Init();



    /* 主循环 - 1MHz正弦波输出，幅度0.4V */
    uint32_t sample_count = 0;

    // 简化的1MHz正弦波生成
    // 使用168个采样点生成1MHz正弦波 (168MHz/168 = 1MHz)
    const uint16_t sine_samples = 168;

    while (1)
    {
        /* 生成1MHz正弦波 */
        // 计算当前采样点的角度
        float angle = (float)sample_count * 2.0f * 3.14159f / (float)sine_samples;
        float sine_value = sinf(angle);  // -1 到 1

        // 转换为0.4V峰峰值 (DAC参考电压5V)
        // 0.4V峰峰值 = ±0.2V幅度
        // 中心电压 = 2.5V = 32768 (DAC值)
        // 0.2V幅度 = (0.2/5.0) * 65535 = 2621 (DAC值)
        uint16_t dac_value = (uint16_t)(32768 + sine_value * 2621);

        // 输出到DAC
        DAC8552_Write(DAC8552_CHANNEL_A, dac_value);

        // 更新采样计数器
        sample_count++;
        if (sample_count >= sine_samples) {
            sample_count = 0;
        }

        /* 高频输出控制 - 最小延时 */
        // 使用最小延时确保波形稳定
        for (volatile int i = 0; i < 10; i++);
    }
}

// ==================== DDS高精度正弦波生成 ====================

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置为168MHz */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif



/**
  * @brief  TIM6中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void DDS_TIM6_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用DDS定时器中断
    // 使用软件生成正弦波
}

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}




