<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>CONTROL_Type Union Reference</title>
<title>CMSIS-CORE: CONTROL_Type Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('union_c_o_n_t_r_o_l___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">CONTROL_Type Union Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Union type to access the Control Registers (CONTROL).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:adc6a38ab2980d0e9577b5a871da14eb9"><td class="memItemLeft" >struct {</td></tr>
<tr class="memitem:a666f4d16841194dd2ffb38cd9c1ff021"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_c_o_n_t_r_o_l___type.html#a35c1732cf153b7b5c4bd321cf1de9605">nPRIV</a>:1</td></tr>
<tr class="memdesc:a666f4d16841194dd2ffb38cd9c1ff021"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 0 Execution privilege in Thread mode  <a href="#a666f4d16841194dd2ffb38cd9c1ff021"></a><br/></td></tr>
<tr class="separator:a666f4d16841194dd2ffb38cd9c1ff021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae452742bb12b77c4cae20418495334f1"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_c_o_n_t_r_o_l___type.html#a8cc085fea1c50a8bd9adea63931ee8e2">SPSEL</a>:1</td></tr>
<tr class="memdesc:ae452742bb12b77c4cae20418495334f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 1 Stack to be used  <a href="#ae452742bb12b77c4cae20418495334f1"></a><br/></td></tr>
<tr class="separator:ae452742bb12b77c4cae20418495334f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63fd27005fb7c3828f9f145a4fccf9a8"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_c_o_n_t_r_o_l___type.html#ac62cfff08e6f055e0101785bad7094cd">FPCA</a>:1</td></tr>
<tr class="memdesc:a63fd27005fb7c3828f9f145a4fccf9a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 2 FP extension active flag  <a href="#a63fd27005fb7c3828f9f145a4fccf9a8"></a><br/></td></tr>
<tr class="separator:a63fd27005fb7c3828f9f145a4fccf9a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada408fafd29cbe29e0c71ef479bd7564"><td class="memItemLeft" >&#160;&#160;&#160;uint32_t&#160;&#160;&#160;<a class="el" href="union_c_o_n_t_r_o_l___type.html#af8c314273a1e4970a5671bd7f8184f50">_reserved0</a>:29</td></tr>
<tr class="memdesc:ada408fafd29cbe29e0c71ef479bd7564"><td class="mdescLeft">&#160;</td><td class="mdescRight">bit: 3..31 Reserved  <a href="#ada408fafd29cbe29e0c71ef479bd7564"></a><br/></td></tr>
<tr class="separator:ada408fafd29cbe29e0c71ef479bd7564"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc6a38ab2980d0e9577b5a871da14eb9"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="union_c_o_n_t_r_o_l___type.html#adc6a38ab2980d0e9577b5a871da14eb9">b</a></td></tr>
<tr class="memdesc:adc6a38ab2980d0e9577b5a871da14eb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Structure used for bit access.  <a href="#adc6a38ab2980d0e9577b5a871da14eb9"></a><br/></td></tr>
<tr class="separator:adc6a38ab2980d0e9577b5a871da14eb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b642cca3d96da660b1198c133ca2a1f"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="union_c_o_n_t_r_o_l___type.html#a6b642cca3d96da660b1198c133ca2a1f">w</a></td></tr>
<tr class="memdesc:a6b642cca3d96da660b1198c133ca2a1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used for word access.  <a href="#a6b642cca3d96da660b1198c133ca2a1f"></a><br/></td></tr>
<tr class="separator:a6b642cca3d96da660b1198c133ca2a1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="af8c314273a1e4970a5671bd7f8184f50"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t CONTROL_Type::_reserved0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adc6a38ab2980d0e9577b5a871da14eb9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   CONTROL_Type::b</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac62cfff08e6f055e0101785bad7094cd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t CONTROL_Type::FPCA</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35c1732cf153b7b5c4bd321cf1de9605"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t CONTROL_Type::nPRIV</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8cc085fea1c50a8bd9adea63931ee8e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t CONTROL_Type::SPSEL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6b642cca3d96da660b1198c133ca2a1f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t CONTROL_Type::w</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="union_c_o_n_t_r_o_l___type.html">CONTROL_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
