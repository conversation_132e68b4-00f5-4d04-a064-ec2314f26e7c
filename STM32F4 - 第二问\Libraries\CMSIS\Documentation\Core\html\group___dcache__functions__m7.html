<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>D-Cache Functions</title>
<title>CMSIS-CORE: D-Cache Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___dcache__functions__m7.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">D-Cache Functions</div>  </div>
<div class="ingroups"><a class="el" href="group__cache__functions__m7.html">Cache Functions  (only Cortex-M7)</a></div></div><!--header-->
<div class="contents">

<p>Functions for the data cache.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga63aa640d9006021a796a5dcf9c7180b6"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga63aa640d9006021a796a5dcf9c7180b6">SCB_EnableDCache</a> (void)</td></tr>
<tr class="memdesc:ga63aa640d9006021a796a5dcf9c7180b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable D-Cache.  <a href="#ga63aa640d9006021a796a5dcf9c7180b6"></a><br/></td></tr>
<tr class="separator:ga63aa640d9006021a796a5dcf9c7180b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6468170f90d270caab8116e7a4f0b5fe"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga6468170f90d270caab8116e7a4f0b5fe">SCB_DisableDCache</a> (void)</td></tr>
<tr class="memdesc:ga6468170f90d270caab8116e7a4f0b5fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable D-Cache.  <a href="#ga6468170f90d270caab8116e7a4f0b5fe"></a><br/></td></tr>
<tr class="separator:ga6468170f90d270caab8116e7a4f0b5fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace2d30db08887d0bdb818b8a785a5ce6"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#gace2d30db08887d0bdb818b8a785a5ce6">SCB_InvalidateDCache</a> (void)</td></tr>
<tr class="memdesc:gace2d30db08887d0bdb818b8a785a5ce6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invalidate D-Cache.  <a href="#gace2d30db08887d0bdb818b8a785a5ce6"></a><br/></td></tr>
<tr class="separator:gace2d30db08887d0bdb818b8a785a5ce6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga55583e3065c6eabca204b8b89b121c4c"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga55583e3065c6eabca204b8b89b121c4c">SCB_CleanDCache</a> (void)</td></tr>
<tr class="memdesc:ga55583e3065c6eabca204b8b89b121c4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clean D-Cache.  <a href="#ga55583e3065c6eabca204b8b89b121c4c"></a><br/></td></tr>
<tr class="separator:ga55583e3065c6eabca204b8b89b121c4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1b741def9e3b2ca97dc9ea49b8ce505c"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga1b741def9e3b2ca97dc9ea49b8ce505c">SCB_CleanInvalidateDCache</a> (void)</td></tr>
<tr class="memdesc:ga1b741def9e3b2ca97dc9ea49b8ce505c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clean &amp; Invalidate D-Cache.  <a href="#ga1b741def9e3b2ca97dc9ea49b8ce505c"></a><br/></td></tr>
<tr class="separator:ga1b741def9e3b2ca97dc9ea49b8ce505c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga503ef7ef58c0773defd15a82f6336c09"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga503ef7ef58c0773defd15a82f6336c09">SCB_InvalidateDCache_by_Addr</a> (uint32_t *addr, int32_t dsize)</td></tr>
<tr class="memdesc:ga503ef7ef58c0773defd15a82f6336c09"><td class="mdescLeft">&#160;</td><td class="mdescRight">D-Cache Invalidate by address.  <a href="#ga503ef7ef58c0773defd15a82f6336c09"></a><br/></td></tr>
<tr class="separator:ga503ef7ef58c0773defd15a82f6336c09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga696fadbf7b9cc71dad42fab61873a40d"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga696fadbf7b9cc71dad42fab61873a40d">SCB_CleanDCache_by_Addr</a> (uint32_t *addr, int32_t dsize)</td></tr>
<tr class="memdesc:ga696fadbf7b9cc71dad42fab61873a40d"><td class="mdescLeft">&#160;</td><td class="mdescRight">D-Cache Clean by address.  <a href="#ga696fadbf7b9cc71dad42fab61873a40d"></a><br/></td></tr>
<tr class="separator:ga696fadbf7b9cc71dad42fab61873a40d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga630131b2572eaa16b569ed364dfc895e"><td class="memItemLeft" align="right" valign="top">__STATIC_INLINE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___dcache__functions__m7.html#ga630131b2572eaa16b569ed364dfc895e">SCB_CleanInvalidateDCache_by_Addr</a> (uint32_t *addr, int32_t dsize)</td></tr>
<tr class="memdesc:ga630131b2572eaa16b569ed364dfc895e"><td class="mdescLeft">&#160;</td><td class="mdescRight">D-Cache Clean and Invalidate by address.  <a href="#ga630131b2572eaa16b569ed364dfc895e"></a><br/></td></tr>
<tr class="separator:ga630131b2572eaa16b569ed364dfc895e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>// close ICache functions </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga55583e3065c6eabca204b8b89b121c4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_CleanDCache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function cleans the entire data cache. </p>

</div>
</div>
<a class="anchor" id="ga696fadbf7b9cc71dad42fab61873a40d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_CleanDCache_by_Addr </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>dsize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>address (aligned to 32-byte boundary) </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">dsize</td><td>size of memory block (in number of bytes)</td></tr>
  </table>
  </dd>
</dl>
<p>The function cleans a memory block of size <em>dsize</em> [bytes] starting at address <em>address</em>. The address is aligned to 32-byte boundry. </p>

</div>
</div>
<a class="anchor" id="ga1b741def9e3b2ca97dc9ea49b8ce505c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_CleanInvalidateDCache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function cleans and invalidates the entire data cache. </p>

</div>
</div>
<a class="anchor" id="ga630131b2572eaa16b569ed364dfc895e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_CleanInvalidateDCache_by_Addr </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>dsize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>address (aligned to 32-byte boundary) </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">dsize</td><td>size of memory block (in number of bytes)</td></tr>
  </table>
  </dd>
</dl>
<p>The function invalidates and cleans a memory block of size <em>dsize</em> [bytes] starting at address <em>address</em>. The address is aligned to 32-byte boundry. </p>

</div>
</div>
<a class="anchor" id="ga6468170f90d270caab8116e7a4f0b5fe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_DisableDCache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function turns off the entire data cache.</p>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>When disabling the data cache, you must clean (<a class="el" href="group___dcache__functions__m7.html#ga55583e3065c6eabca204b8b89b121c4c">SCB_CleanDCache</a>) the entire cache to ensure that any dirty data is flushed to external memory. </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga63aa640d9006021a796a5dcf9c7180b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_EnableDCache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function turns on the entire data cache. </p>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>Before enabling the data cache, you must invalidate the entire data cache (<a class="el" href="group___dcache__functions__m7.html#gace2d30db08887d0bdb818b8a785a5ce6">SCB_InvalidateDCache</a>), because external memory might have changed from when the cache was disabled. </li>
</ul>
</dd>
<dd>
<ul>
<li>After reset, you must invalidate (<a class="el" href="group___dcache__functions__m7.html#gace2d30db08887d0bdb818b8a785a5ce6">SCB_InvalidateDCache</a>) each cache before enabling it. </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gace2d30db08887d0bdb818b8a785a5ce6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_InvalidateDCache </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function invalidates the entire data cache.</p>
<dl class="section note"><dt>Note</dt><dd><ul>
<li>After reset, you must invalidate each cache before enabling (<a class="el" href="group___dcache__functions__m7.html#ga63aa640d9006021a796a5dcf9c7180b6">SCB_EnableDCache</a>) it. </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga503ef7ef58c0773defd15a82f6336c09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__STATIC_INLINE void SCB_InvalidateDCache_by_Addr </td>
          <td>(</td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>addr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>dsize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">addr</td><td>address (aligned to 32-byte boundary) </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">dsize</td><td>size of memory block (in number of bytes)</td></tr>
  </table>
  </dd>
</dl>
<p>The function invalidates a memory block of size <em>dsize</em> [bytes] starting at address <em>address</em>. The address is aligned to 32-byte boundry. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
