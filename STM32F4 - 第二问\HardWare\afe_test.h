/**
  ******************************************************************************
  * @file    afe_test.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   AFE板测试模块头文件 - LTC2248 ADC测试
  ******************************************************************************
  */

#ifndef __AFE_TEST_H
#define __AFE_TEST_H

#include "stm32f4xx.h"

//==============================================================================
// ADC硬件连接定义 - 梁山派·天空星STM32F407VGT6开发板
//==============================================================================

// ADC数据线连接 (PC0-PC11) - 梁山派天空星引脚标记 (每个字母00-15)
// 根据引脚图：PC0=C00, PC1=C01, PC2=C02, PC3=C03, PC4=C04, PC5=C05
// PC6=C06, PC7=C07, PC8=C08, PC9=C09, PC10=C10, PC11=C11
#define ADC_DATA_GPIO_PORT          GPIOC
#define ADC_DATA_GPIO_CLK           RCC_AHB1Periph_GPIOC
#define ADC_DATA_PINS               (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                     GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 | \
                                     GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11)

// ADC时钟线连接 (PA0) - 梁山派天空星引脚标记 A00
#define ADC_CLK_PIN                 GPIO_Pin_0
#define ADC_CLK_GPIO_PORT           GPIOA
#define ADC_CLK_GPIO_CLK            RCC_AHB1Periph_GPIOA
#define ADC_CLK_EXTI_LINE           EXTI_Line0
#define ADC_CLK_EXTI_PORT_SOURCE    EXTI_PortSourceGPIOA
#define ADC_CLK_EXTI_PIN_SOURCE     EXTI_PinSource0
#define ADC_CLK_EXTI_IRQ            EXTI0_IRQn

//==============================================================================
// 数据缓存定义
//==============================================================================

#define ADC_BUFFER_SIZE             256             // 缓存大小
#define ADC_DATA_MASK               0x0FFF          // 12位数据掩码

//==============================================================================
// 全局变量声明
//==============================================================================

extern uint16_t g_adc_buffer[ADC_BUFFER_SIZE];      // ADC数据缓存
extern volatile uint8_t g_data_ready_flag;          // 数据准备就绪标志
extern volatile uint16_t g_buffer_index;            // 缓存索引

//==============================================================================
// 数据统计结构体
//==============================================================================

typedef struct {
    uint16_t current_value;                         // 当前值
    uint16_t max_value;                             // 最大值
    uint16_t min_value;                             // 最小值
    uint32_t average_value;                         // 平均值
    uint32_t sample_count;                          // 采样计数
} ADC_Stats_t;

//==============================================================================
// 函数声明
//==============================================================================

// 初始化函数
void AFE_GPIO_Init(void);
void AFE_EXTI_Init(void);
void AFE_Test_Init(void);

// 数据处理函数
void AFE_ProcessData(ADC_Stats_t *stats);
void AFE_CalculateStats(uint16_t *buffer, uint16_t length, ADC_Stats_t *stats);

// 中断控制函数
void AFE_EnableInterrupt(void);
void AFE_DisableInterrupt(void);

// 工具函数
uint16_t AFE_ReadADCData(void);
void AFE_ClearBuffer(void);

#endif /* __AFE_TEST_H */
